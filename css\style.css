/* Modern Design System Variables */
:root {
  /* Primary Colors */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

  /* Solid Colors */
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --accent-color: #4facfe;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;

  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-heading: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  --space-4xl: 6rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-backdrop: blur(20px);

  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--gray-800);
  background: var(--white);
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: 1.2;
  color: var(--gray-900);
  margin-bottom: var(--space-md);
}

h1 { font-size: clamp(2rem, 5vw, 3.5rem); }
h2 { font-size: clamp(1.75rem, 4vw, 2.5rem); }
h3 { font-size: clamp(1.5rem, 3vw, 2rem); }
h4 { font-size: clamp(1.25rem, 2.5vw, 1.5rem); }

p {
  margin-bottom: var(--space-md);
  color: var(--gray-600);
  line-height: 1.7;
}

/* Links */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all var(--transition-fast);
}

a:hover {
  color: var(--secondary-color);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-md);
  }
}

/* Section Styles */
.section {
  padding: var(--space-4xl) 0;
  position: relative;
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-3xl);
}

.section-title {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-md);
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
}

.section-divider {
  width: 80px;
  height: 4px;
  background: var(--accent-gradient);
  margin: var(--space-lg) auto;
  border-radius: var(--radius-sm);
}


/* Navigation */
.navbar {
  position: sticky;
  top: 0;
  background: var(--glass-bg);
  box-shadow: 0 2px 16px rgba(26,115,232,0.08);
  z-index: 1000;
  backdrop-filter: blur(8px);
  border-bottom: 1px solid #e0e0e0;
  transition: background 0.3s;
}
.glassy {
  background: var(--glass-bg);
  backdrop-filter: blur(12px);
}
.nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.logo {
  font-weight: 900;
  font-size: 1.5rem;
  color: var(--primary-blue);
  letter-spacing: 1px;
  text-shadow: 0 2px 8px rgba(26,115,232,0.08);
}
.highlight {
  color: var(--accent-gold);
}
.nav-links {
  list-style: none;
  display: flex;
  gap: 2rem;
  margin: 0;
  padding: 0;
}
.nav-links a {
  color: var(--dark-grey);
  font-weight: 700;
  font-size: 1.05rem;
  padding: 0.3rem 0.7rem;
  border-radius: 4px;
  transition: background 0.2s, color 0.2s;
}
.nav-links a.active,
.nav-links a:hover {
  background: var(--primary-blue);
  color: var(--white);
}


/* Hero Section */
.hero {
  text-align: center;
  padding: 6rem 2rem 5rem 2rem;
  position: relative;
  background: linear-gradient(120deg, #1a73e8 0%, #0d47a1 100%);
  color: var(--white);
  overflow: hidden;
}
.dramatic-bg {
  background: linear-gradient(120deg, #1a73e8 0%, #0d47a1 100%);
  color: var(--white);
}
.hero-logo-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1.5rem;
}
.hero-logo {
  max-width: 170px;
  filter: drop-shadow(0 4px 24px rgba(26,115,232,0.18));
}
.main-heading {
  color: var(--white);
  text-shadow: 0 2px 16px rgba(13,71,161,0.18);
}
.founder {
  font-weight: 600;
  color: var(--accent-gold);
  margin: 1.2rem 0 0.5rem 0;
  font-size: 1.2rem;
}
.divider {
  color: var(--accent-teal);
  font-weight: 900;
}
.hero-desc {
  max-width: 700px;
  margin: 1.5rem auto 0 auto;
  color: #e3e3e3;
  font-size: 1.15rem;
}
.btn-primary, .btn-cta {
  display: inline-block;
  margin-top: 2.2rem;
  padding: 1rem 2.2rem;
  background: var(--accent-teal);
  color: var(--white);
  border-radius: 6px;
  font-size: 1.1rem;
  font-weight: 700;
  box-shadow: 0 2px 12px rgba(0,191,174,0.13);
  letter-spacing: 0.5px;
  transition: background 0.2s, transform 0.2s;
}
.btn-primary:hover, .btn-cta:hover {
  background: var(--accent-gold);
  color: var(--dark-grey);
  transform: translateY(-2px) scale(1.04);
}
.hero-shape {
  position: absolute;
  bottom: -60px;
  left: 50%;
  transform: translateX(-50%);
  width: 120vw;
  height: 180px;
  background: radial-gradient(ellipse at center, #fff 0%, #e8f0fe 100%);
  opacity: 0.18;
  z-index: 0;
  border-radius: 50% 50% 0 0;
}

.btn-primary {
  display: inline-block;
  margin-top: 2rem;
  padding: 0.8rem 1.5rem;
  background: var(--primary-blue);
  color: var(--white);
  border-radius: 5px;
}


/* Section Styling */
.section {
  padding: 4rem 2rem;
}
.light-bg {
  background-color: var(--light-grey);
}
.white-bg {
  background-color: var(--white);
}
.animated-fadein {
  opacity: 0;
  animation: fadeIn 1.2s ease 0.2s forwards;
}
.animated-slidein {
  opacity: 0;
  transform: translateY(40px);
  animation: slideIn 1.1s cubic-bezier(.4,1.4,.6,1) 0.3s forwards;
}
.animated-pop {
  opacity: 0;
  transform: scale(0.96);
  animation: popIn 1s cubic-bezier(.4,1.4,.6,1) 0.5s forwards;
}
@keyframes fadeIn {
  to { opacity: 1; }
}
@keyframes slideIn {
  to { opacity: 1; transform: none; }
}
@keyframes popIn {
  60% { opacity: 1; transform: scale(1.04); }
  100% { opacity: 1; transform: scale(1); }
}


/* Projects Grid & Cards */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2.2rem;
  margin-top: 2.5rem;
}
.project-card {
  background: var(--white);
  border: 1.5px solid #e0e0e0;
  padding: 2rem 1.5rem 1.5rem 1.5rem;
  border-radius: 14px;
  box-shadow: 0 4px 24px rgba(26,115,232,0.07);
  text-align: left;
  position: relative;
  transition: box-shadow 0.2s, transform 0.2s;
  overflow: hidden;
}
.project-card:hover {
  box-shadow: 0 8px 32px rgba(26,115,232,0.13);
  transform: translateY(-4px) scale(1.02);
}
.project-img-wrap {
  position: relative;
  margin-bottom: 1.2rem;
}
.project-img-wrap img {
  width: 100%;
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(26,115,232,0.08);
}
.badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: var(--accent-gold);
  color: var(--dark-grey);
  font-size: 0.85rem;
  font-weight: 700;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(255,214,0,0.13);
  letter-spacing: 0.5px;
}
.badge-ai {
  background: var(--accent-teal);
  color: var(--white);
}
.badge-dev {
  background: var(--primary-blue);
  color: var(--white);
}
.project-card h3 {
  margin-top: 0.5rem;
  color: var(--dark-blue);
  font-size: 1.3rem;
}
.project-card .tagline {
  font-style: italic;
  color: #666;
  margin-bottom: 0.7rem;
}
.project-meta {
  font-size: 0.98rem;
  color: #888;
  margin: 0.7rem 0 0.2rem 0;
}
.meta-label {
  font-weight: 700;
  color: var(--primary-blue);
}
.btn-secondary {
  display: inline-block;
  margin-top: 1rem;
  padding: 0.7rem 1.4rem;
  background: var(--primary-blue);
  color: var(--white);
  border-radius: 5px;
  font-weight: 700;
  transition: background 0.2s, transform 0.2s;
}
.btn-secondary:hover {
  background: var(--accent-teal);
  color: var(--white);
  transform: scale(1.04);
}


.project-card {
  margin-bottom: 4rem;
}

.project-card img {
  width: 100%;
  max-height: 400px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.project-card h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.project-card ul {
  margin: 0.5rem 0 1rem 1.5rem;
  list-style: disc;
}

.project-card .tagline {
  font-style: italic;
  color: #666;
}

hr {
  border: none;
  border-top: 1px solid #ddd;
  margin: 3rem 0;
}



/* Contact Section */
.contact {
  text-align: left;
  background: var(--primary-blue);
  color: var(--white);
  border-radius: 0 0 32px 32px;
  box-shadow: 0 2px 24px rgba(26,115,232,0.10);
  position: relative;
}
.contact-bg {
  background: linear-gradient(120deg, #1a73e8 0%, #00bfae 100%);
}
.contact-flex {
  display: flex;
  align-items: center;
  gap: 2.5rem;
  flex-wrap: wrap;
}
.contact-info-block {
  flex: 2;
}
.contact-links {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.contact-link {
  color: var(--white);
  font-weight: 700;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.7rem;
  transition: color 0.2s;
}
.contact-link:hover {
  color: var(--accent-gold);
}
.contact-accent {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.contact-img {
  max-width: 220px;
  border-radius: 18px;
  box-shadow: 0 2px 16px rgba(0,191,174,0.13);
}

.contact-container {
  max-width: 800px;
  margin: auto;
  padding-top: 2rem;
}

.contact-info ul.contact-list {
  list-style: none;
  padding: 0;
  margin: 1.5rem 0;
  font-size: 1.1rem;
}

.contact-list li {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.contact-list i {
  color: var(--primary-blue);
  min-width: 20px;
}

.contact-form form {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-form label {
  font-weight: bold;
  display: block;
  margin-bottom: 0.5rem;
}

.contact-form input,
.contact-form textarea {
  padding: 0.9rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 1rem;
  width: 100%;
}

.contact-form .btn-primary {
  width: fit-content;
  background: var(--primary-blue);
  color: white;
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  cursor: pointer;
}

.contact-form .btn-primary:hover {
  background: var(--dark-blue);
}

.form-note {
  font-size: 0.9rem;
  color: #666;
  margin-top: 1rem;
  text-align: center;
}



/* Footer */
.footer {
  background: var(--dark-grey);
  color: var(--white);
  text-align: center;
  padding: 2rem 1rem;
}
.gradient-footer {
  background: var(--footer-gradient);
  color: var(--white);
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  box-shadow: 0 -2px 24px rgba(26,115,232,0.10);
}
.footer-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.footer-social {
  display: flex;
  gap: 1.2rem;
}
.footer-icon {
  color: var(--white);
  font-size: 1.5rem;
  transition: color 0.2s;
}
.footer-icon:hover {
  color: var(--accent-gold);
}


.about-container {
  max-width: 1000px;
  margin: auto;
  padding-top: 2rem;
}

.about-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  margin-top: 2rem;
}

.about-text {
  flex: 1 1 60%;
}

.about-photo {
  flex: 1 1 35%;
  text-align: center;
}

.about-photo img {
  max-width: 100%;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.about-photo blockquote {
  font-style: italic;
  color: #555;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.advantage-card {
  background: var(--light-grey);
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
}

.advantage-card i {
  font-size: 1.5rem;
  color: var(--primary-blue);
  margin-bottom: 0.5rem;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.value-box {
  background: #f0f0f0;
  padding: 1.2rem;
  border-left: 4px solid var(--primary-blue);
  border-radius: 6px;
}

.about-cta {
  text-align: center;
  margin-top: 4rem;
}

.cta-buttons {
  margin-top: 1.5rem;
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive & Utility */
@media (max-width: 1024px) {
  .container {
    padding: 1.2rem;
  }
  .about-flex, .contact-flex, .footer-flex {
    flex-direction: column;
    gap: 1.5rem;
    align-items: flex-start;
  }
  .contact-img {
    max-width: 140px;
  }
}
@media (max-width: 768px) {
  .main-heading {
    font-size: 1.5rem;
  }
  .section-heading {
    font-size: 1.2rem;
  }
  .hero {
    padding: 3rem 1rem 2rem 1rem;
  }
  .projects-grid {
    grid-template-columns: 1fr;
  }
  .about-img, .contact-img {
    display: none;
  }
}

/* Icon Font Placeholders (for demo, replace with real icons or font-awesome if desired) */
.icon-envelope::before { content: '\2709'; display: inline-block; margin-right: 0.4em; }
.icon-linkedin::before { content: '\f0e1'; font-family: 'Font Awesome 6 Free'; font-weight: 900; margin-right: 0.4em; }
.icon-github::before { content: '\f09b'; font-family: 'Font Awesome 6 Free'; font-weight: 900; margin-right: 0.4em; }
*/

/* c:\Users\<USER>\Desktop\web-portfolio\css\style.css
:root {
  --primary-blue: #1a73e8;
  --dark-blue: #0d47a1;
  --light-grey: #f5f5f5;
  --dark-grey: #333333;
  --white: #ffffff;
    --heading-font: 'Montserrat', sans-serif;
  --body-font: 'Inter', sans-serif;
}                   

/* Global Styles */
body {
  margin: 0;
  font-family: var(--body-font);
  color: var(--dark-grey);
  background-color: var(--white);
}   

h1, h2, h3 {
  font-family: var(--heading-font);
  color: var(--dark-grey);
}

.container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 2rem;
}  

a {
  text-decoration: none;
  color: var(--primary-blue);
}

a:hover {
  text-decoration: underline;
}   

/* Navigation */
.navbar {       
  position: sticky;     
  top: 0;
  background: var(--white);
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  z-index: 1000;
}   

.nav-content {          
  display: flex;
  justify-content: space-between;
  align-items: center;
}   

.logo {             
  font-weight: bold;
  font-size: 1.2rem;
  color: var(--dark-grey);
}   

.nav-links {             
  list-style: none;
  display: flex;
  gap: 1.5rem;
}   

.nav-links a {             
  color: var(--dark-grey);
  font-weight: 600;
}   

/* Hero Section */